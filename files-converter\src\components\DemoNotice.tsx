'use client'

import { Card, CardContent } from '@/components/ui/Card'
import { AlertTriangle, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/Button'

export function DemoNotice() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const isDemo = !supabaseUrl || supabaseUrl.includes('placeholder')

  if (!isDemo) return null

  return (
    <Card className="mb-6 border-blue-200 bg-blue-50">
      <CardContent className="p-4">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <h3 className="font-medium text-blue-900 mb-1">
              Demo Mode
            </h3>
            <p className="text-blue-800 text-sm mb-3">
              This is a demo version. To enable full functionality including authentication and 
              conversion history, please set up Supabase by following the setup instructions in the README.
            </p>
            <div className="flex gap-2">
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => window.open('https://supabase.com', '_blank')}
                className="text-blue-700 border-blue-300 hover:bg-blue-100"
              >
                <ExternalLink className="h-3 w-3 mr-1" />
                Get Supabase
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
