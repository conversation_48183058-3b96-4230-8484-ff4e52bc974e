{"name": "@supabase/ssr", "version": "0.7.0", "description": "Use the Supabase JavaScript library in popular server-side rendering (SSR) frameworks.", "main": "dist/main/index.js", "module": "dist/module/index.js", "scripts": {"build": "tsc && tsc -p tsconfig.main.json", "test": "vitest --coverage"}, "repository": {"type": "git", "url": "git+https://github.com/supabase/ssr.git"}, "keywords": ["Supabase", "SSR", "Server-Side", "Rendering", "Next", "Next.js", "NextJS", "Remix", "Svelte", "SvelteKit", "Postgres"], "author": "Supabase, Inc.", "license": "MIT", "bugs": {"url": "https://github.com/supabase/ssr/issues"}, "homepage": "https://github.com/supabase/ssr#readme", "devDependencies": {"@eslint/js": "^9.3.0", "@supabase/supabase-js": "^2.43.4", "@vitest/coverage-v8": "^1.6.0", "eslint": "^8.57.0", "prettier": "^3.2.5", "typescript": "^5.4.5", "typescript-eslint": "^7.10.0", "vitest": "^1.6.0"}, "peerDependencies": {"@supabase/supabase-js": "^2.43.4"}, "dependencies": {"cookie": "^1.0.2"}}