{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SAAS%20Doc%20converter/files-converter/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\n\nexport function createClient() {\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('placeholder')) {\n    throw new Error('Supabase configuration is missing. Please set up your environment variables.')\n  }\n\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n"], "names": [], "mappings": ";;;;AAGsB;AAHtB;AAAA;;AAEO,SAAS;IACd,MAAM;IACN,MAAM;IAEN,IAAI,CAAC,eAAe,CAAC,mBAAmB,YAAY,QAAQ,CAAC,gBAAgB;QAC3E,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,IAAA,oMAAmB,EAAC,aAAa;AAC1C", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SAAS%20Doc%20converter/files-converter/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClient } from '@/lib/supabase/client'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  // Check if Supabase is configured\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n  const isSupabaseConfigured = supabaseUrl && supabaseAnonKey && !supabaseUrl.includes('placeholder')\n\n  let supabase: any = null\n\n  if (isSupabaseConfigured) {\n    try {\n      supabase = createClient()\n    } catch (error) {\n      console.warn('Supabase client creation failed:', error)\n    }\n  }\n\n  useEffect(() => {\n    if (!isSupabaseConfigured || !supabase) {\n      setLoading(false)\n      return\n    }\n\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session } } = await supabase.auth.getSession()\n        setUser(session?.user ?? null)\n      } catch (error) {\n        console.error('Error getting session:', error)\n      }\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [isSupabaseConfigured])\n\n  const signIn = async (email: string, password: string) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { error: { message: 'Authentication service not configured' } }\n    }\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n      return { error }\n    } catch (error) {\n      return { error: { message: 'Authentication failed' } }\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName?: string) => {\n    if (!isSupabaseConfigured || !supabase) {\n      return { error: { message: 'Authentication service not configured' } }\n    }\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          },\n        },\n      })\n      return { error }\n    } catch (error) {\n      return { error: { message: 'Registration failed' } }\n    }\n  }\n\n  const signOut = async () => {\n    if (!isSupabaseConfigured || !supabase) return\n\n    try {\n      await supabase.auth.signOut()\n    } catch (error) {\n      console.error('Sign out error:', error)\n    }\n  }\n\n  const value = {\n    user,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;;AAqBsB;;AAnBtB;AAEA;;;AAJA;;;AAcA,MAAM,4BAAc,IAAA,8KAAa,EAA8B;AAExD,SAAS,aAAa,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,kCAAkC;IAClC,MAAM;IACN,MAAM;IACN,MAAM,uBAAuB,eAAe,mBAAmB,CAAC,YAAY,QAAQ,CAAC;IAErF,IAAI,WAAgB;IAEpB,IAAI,sBAAsB;QACxB,IAAI;YACF,WAAW,IAAA,mJAAY;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,oCAAoC;QACnD;IACF;IAEA,IAAA,0KAAS;kCAAC;YACR,IAAI,CAAC,wBAAwB,CAAC,UAAU;gBACtC,WAAW;gBACX;YACF;YAEA,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;4BACpD;wBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBAC3B,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0BAA0B;oBAC1C;oBACA,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;wBACJ;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG;QAAC;KAAqB;IAEzB,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAwC;YAAE;QACvE;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YACA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAwB;YAAE;QACvD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI,CAAC,wBAAwB,CAAC,UAAU;YACtC,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAwC;YAAE;QACvE;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YACA,OAAO;gBAAE;YAAM;QACjB,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,OAAO;oBAAE,SAAS;gBAAsB;YAAE;QACrD;IACF;IAEA,MAAM,UAAU;QACd,IAAI,CAAC,wBAAwB,CAAC,UAAU;QAExC,IAAI;YACF,MAAM,SAAS,IAAI,CAAC,OAAO;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GA7GgB;KAAA;AA+GT,SAAS;;IACd,MAAM,UAAU,IAAA,2KAAU,EAAC;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SAAS%20Doc%20converter/files-converter/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return '0 Bytes'\n  \n  const k = 1024\n  const sizes = ['Bytes', 'KB', 'MB', 'GB']\n  const i = Math.floor(Math.log(bytes) / Math.log(k))\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n}\n\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf(\".\") - 1 >>> 0) + 2)\n}\n\nexport function isValidFileType(file: File, allowedTypes: string[]): boolean {\n  const fileExtension = getFileExtension(file.name).toLowerCase()\n  return allowedTypes.includes(fileExtension)\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IAExB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AACzE;AAEO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAEO,SAAS,gBAAgB,IAAU,EAAE,YAAsB;IAChE,MAAM,gBAAgB,iBAAiB,KAAK,IAAI,EAAE,WAAW;IAC7D,OAAO,aAAa,QAAQ,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SAAS%20Doc%20converter/files-converter/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive: \"bg-red-500 text-white hover:bg-red-600\",\n        outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,2KAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/SAAS%20Doc%20converter/files-converter/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/Button'\nimport { FileText, User, LogOut } from 'lucide-react'\nimport { useState } from 'react'\n\nexport function Header() {\n  const { user, signOut } = useAuth()\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <FileText className=\"h-8 w-8 text-blue-600\" />\n            <span className=\"text-xl font-bold text-gray-900\">Files Converter</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link href=\"/dashboard\" className=\"text-gray-600 hover:text-gray-900\">\n              Convert\n            </Link>\n            <Link href=\"/pricing\" className=\"text-gray-600 hover:text-gray-900\">\n              Pricing\n            </Link>\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/profile\" className=\"flex items-center space-x-1 text-gray-600 hover:text-gray-900\">\n                  <User className=\"h-4 w-4\" />\n                  <span>Profile</span>\n                </Link>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => signOut()}\n                  className=\"flex items-center space-x-1\"\n                >\n                  <LogOut className=\"h-4 w-4\" />\n                  <span>Sign out</span>\n                </Button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-4\">\n                <Link href=\"/login\">\n                  <Button variant=\"ghost\" size=\"sm\">\n                    Sign in\n                  </Button>\n                </Link>\n                <Link href=\"/signup\">\n                  <Button size=\"sm\">\n                    Get started\n                  </Button>\n                </Link>\n              </div>\n            )}\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2\"\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t\">\n            <div className=\"flex flex-col space-y-4\">\n              <Link href=\"/dashboard\" className=\"text-gray-600 hover:text-gray-900\">\n                Convert\n              </Link>\n              <Link href=\"/pricing\" className=\"text-gray-600 hover:text-gray-900\">\n                Pricing\n              </Link>\n              {user ? (\n                <>\n                  <Link href=\"/profile\" className=\"text-gray-600 hover:text-gray-900\">\n                    Profile\n                  </Link>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => signOut()}\n                    className=\"justify-start\"\n                  >\n                    Sign out\n                  </Button>\n                </>\n              ) : (\n                <div className=\"flex flex-col space-y-2\">\n                  <Link href=\"/login\">\n                    <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-start\">\n                      Sign in\n                    </Button>\n                  </Link>\n                  <Link href=\"/signup\">\n                    <Button size=\"sm\" className=\"w-full\">\n                      Get started\n                    </Button>\n                  </Link>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,IAAA,6IAAO;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,0KAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC,6NAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0KAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAoC;;;;;;8CAGtE,6LAAC,0KAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAoC;;;;;;gCAGnE,qBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,0KAAI;4CAAC,MAAK;4CAAW,WAAU;;8DAC9B,6LAAC,6MAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC,+IAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM;4CACf,WAAU;;8DAEV,6LAAC,uNAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;yDAIV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,0KAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,+IAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAAK;;;;;;;;;;;sDAIpC,6LAAC,0KAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,+IAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;;;;;;;sCAS1B,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,CAAC;sCAE9B,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9D,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAM1E,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAI;gCAAC,MAAK;gCAAa,WAAU;0CAAoC;;;;;;0CAGtE,6LAAC,0KAAI;gCAAC,MAAK;gCAAW,WAAU;0CAAoC;;;;;;4BAGnE,qBACC;;kDACE,6LAAC,0KAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAoC;;;;;;kDAGpE,6LAAC,+IAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM;wCACf,WAAU;kDACX;;;;;;;6DAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,0KAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,+IAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAAuB;;;;;;;;;;;kDAIrE,6LAAC,0KAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,+IAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzD;GA7GgB;;QACY,6IAAO;;;KADnB", "debugId": null}}]}