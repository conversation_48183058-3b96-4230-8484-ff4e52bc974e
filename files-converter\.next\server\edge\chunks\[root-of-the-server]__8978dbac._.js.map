{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  let supabaseResponse = NextResponse.next({\n    request,\n  })\n\n  // Skip middleware if Supabase is not configured\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\n\n  if (!supabaseUrl || !supabaseAnonKey || supabaseUrl.includes('placeholder')) {\n    return supabaseResponse\n  }\n\n  const supabase = createServerClient(\n    supabaseUrl,\n    supabaseAnonKey,\n    {\n      cookies: {\n        getAll() {\n          return request.cookies.getAll()\n        },\n        setAll(cookiesToSet) {\n          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))\n          supabaseResponse = NextResponse.next({\n            request,\n          })\n          cookiesToSet.forEach(({ name, value, options }) =>\n            supabaseResponse.cookies.set(name, value, options)\n          )\n        },\n      },\n    }\n  )\n\n  try {\n    // Refresh session if expired - required for Server Components\n    await supabase.auth.getUser()\n  } catch (error) {\n    console.error('Middleware auth error:', error)\n  }\n\n  return supabaseResponse\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * Feel free to modify this pattern to include more paths.\n     */\n    '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,IAAI,mBAAmB,gMAAY,CAAC,IAAI,CAAC;QACvC;IACF;IAEA,gDAAgD;IAChD,MAAM;IACN,MAAM;IAEN,IAAI,CAAC,eAAe,CAAC,mBAAmB,YAAY,QAAQ,CAAC,gBAAgB;QAC3E,OAAO;IACT;IAEA,MAAM,WAAW,IAAA,uMAAkB,EACjC,aACA,iBACA;QACE,SAAS;YACP;gBACE,OAAO,QAAQ,OAAO,CAAC,MAAM;YAC/B;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAK,QAAQ,OAAO,CAAC,GAAG,CAAC,MAAM;gBAC7E,mBAAmB,gMAAY,CAAC,IAAI,CAAC;oBACnC;gBACF;gBACA,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,iBAAiB,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO;YAE9C;QACF;IACF;IAGF,IAAI;QACF,8DAA8D;QAC9D,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;IAC1C;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}