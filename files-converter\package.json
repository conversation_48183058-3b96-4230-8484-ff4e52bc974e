{"name": "files-converter", "version": "0.1.0", "private": true, "description": "A modern SaaS file conversion platform built with Next.js and Supabase", "keywords": ["file-converter", "saas", "nextjs", "supabase", "pdf", "document-conversion"], "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.56.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.541.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}