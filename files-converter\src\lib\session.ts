'use client'

const SESSION_KEY = 'files_converter_session'
const CONVERSION_COUNT_KEY = 'files_converter_count'

export class SessionManager {
  private static instance: SessionManager
  private sessionId: string | null = null

  private constructor() {
    if (typeof window !== 'undefined') {
      this.sessionId = localStorage.getItem(SESSION_KEY)
      if (!this.sessionId) {
        this.sessionId = this.generateSessionId()
        localStorage.setItem(SESSION_KEY, this.sessionId)
      }
    }
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager()
    }
    return SessionManager.instance
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36)
  }

  public getSessionId(): string | null {
    return this.sessionId
  }

  public getLocalConversionCount(): number {
    if (typeof window === 'undefined') return 0
    const count = localStorage.getItem(CONVERSION_COUNT_KEY)
    return count ? parseInt(count, 10) : 0
  }

  public incrementLocalConversionCount(): void {
    if (typeof window === 'undefined') return
    const currentCount = this.getLocalConversionCount()
    localStorage.setItem(CONVERSION_COUNT_KEY, (currentCount + 1).toString())
  }

  public resetLocalConversionCount(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(CONVERSION_COUNT_KEY)
  }

  public clearSession(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(SESSION_KEY)
    localStorage.removeItem(CONVERSION_COUNT_KEY)
    this.sessionId = null
  }
}

export const sessionManager = SessionManager.getInstance()
