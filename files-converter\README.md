# Files Converter - SaaS File Conversion Platform

A modern, professional SaaS application built with Next.js that allows users to convert files between various formats including PDF, DOCX, images, videos, and audio files.

## Features

- 🔄 **Multi-format Support**: Convert between PDF, DOCX, PNG, JPG, MP4, MP3, and more
- 🔐 **Authentication**: Secure login/signup with Supabase Auth
- 📱 **Responsive Design**: Optimized for both mobile and desktop
- 🆓 **Freemium Model**: 5 free conversions for anonymous users, unlimited for registered users
- 📊 **Conversion History**: Track and manage conversion history for logged-in users
- ⚡ **Fast Processing**: Optimized conversion pipeline with progress tracking
- 🎨 **Modern UI**: Clean, professional interface built with Tailwind CSS

## Tech Stack

- **Frontend**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Backend**: Supabase (Authentication, Database, Storage)
- **UI Components**: Custom components with Lucide React icons
- **Styling**: Tailwind CSS with custom design system

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Supabase account

### Installation

1. **Clone and install dependencies**:
```bash
cd files-converter
npm install
```

2. **Set up Supabase**:
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Run the SQL schema in `supabase/schema.sql` in your Supabase SQL editor

3. **Configure environment variables**:
   - Copy `.env.local` and update with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_MAX_ANONYMOUS_CONVERSIONS=5
```

4. **Run the development server**:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Main conversion interface
│   ├── login/            # Authentication pages
│   ├── signup/
│   ├── profile/          # User profile and history
│   ├── pricing/          # Pricing information
│   └── api/              # API routes
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   └── layout/           # Layout components
├── contexts/             # React contexts
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions and configurations
└── types/                # TypeScript type definitions
```

## Key Features Implementation

### Authentication
- Supabase Auth integration with email/password
- Protected routes and middleware
- Session management for anonymous users

### File Conversion
- Drag-and-drop file upload
- Format validation and conversion options
- Progress tracking and status updates
- Download functionality

### User Management
- Anonymous user limits (5 conversions)
- Conversion history for registered users
- Profile management

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy

### Other Platforms
The app can be deployed to any platform that supports Next.js applications.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
