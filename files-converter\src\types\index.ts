export interface User {
  id: string
  email: string
  created_at: string
  updated_at: string
}

export interface ConversionHistory {
  id: string
  user_id?: string
  session_id?: string
  original_filename: string
  original_format: string
  target_format: string
  file_size: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  download_url?: string
  created_at: string
  updated_at: string
}

export interface FileConversion {
  id: string
  file: File
  originalFormat: string
  targetFormat: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  downloadUrl?: string
  error?: string
}

export type SupportedFormat = 
  | 'pdf' | 'docx' | 'doc' | 'txt' | 'rtf'
  | 'png' | 'jpg' | 'jpeg' | 'gif' | 'bmp' | 'webp'
  | 'mp4' | 'avi' | 'mov' | 'wmv' | 'flv'
  | 'mp3' | 'wav' | 'flac' | 'aac' | 'm4a'

export interface ConversionOption {
  from: SupportedFormat[]
  to: SupportedFormat[]
  category: 'document' | 'image' | 'video' | 'audio'
}
