'use client'

import { useCallback, useState } from 'react'
import { Upload, X, FileText, Image as ImageIcon, Video, Music } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { isFormatSupported, getFileCategory } from '@/lib/conversion'
import { formatFileSize } from '@/lib/utils'

interface FileUploadProps {
  onFileSelect: (file: File) => void
  disabled?: boolean
  maxFiles?: number
}

export function FileUpload({ onFileSelect, disabled = false, maxFiles = 10 }: FileUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }, [disabled])

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return
    
    const files = Array.from(e.target.files || [])
    handleFiles(files)
  }

  const handleFiles = (files: File[]) => {
    const validFiles = files.filter(file => {
      const extension = file.name.split('.').pop()?.toLowerCase() || ''
      return isFormatSupported(extension)
    })

    const newFiles = validFiles.slice(0, maxFiles - selectedFiles.length)
    setSelectedFiles(prev => [...prev, ...newFiles])
    
    newFiles.forEach(file => onFileSelect(file))
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  const getFileIcon = (filename: string) => {
    const extension = filename.split('.').pop()?.toLowerCase() || ''
    const category = getFileCategory(extension)
    
    switch (category) {
      case 'document':
        return <FileText className="h-8 w-8 text-blue-600" />
      case 'image':
        return <ImageIcon className="h-8 w-8 text-green-600" />
      case 'video':
        return <Video className="h-8 w-8 text-purple-600" />
      case 'audio':
        return <Music className="h-8 w-8 text-orange-600" />
      default:
        return <FileText className="h-8 w-8 text-gray-600" />
    }
  }

  return (
    <div className="w-full">
      {/* Upload Area */}
      <Card className={`border-2 border-dashed transition-colors ${
        dragActive 
          ? 'border-blue-400 bg-blue-50' 
          : 'border-gray-300 hover:border-gray-400'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}>
        <CardContent className="p-8">
          <div
            className="text-center"
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <Upload className={`mx-auto h-12 w-12 mb-4 ${
              dragActive ? 'text-blue-600' : 'text-gray-400'
            }`} />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {dragActive ? 'Drop files here' : 'Upload your files'}
            </h3>
            <p className="text-gray-600 mb-4">
              Drag and drop files here, or click to browse
            </p>
            <input
              type="file"
              multiple
              onChange={handleFileInput}
              disabled={disabled}
              className="hidden"
              id="file-upload"
              accept=".pdf,.docx,.doc,.txt,.rtf,.png,.jpg,.jpeg,.gif,.bmp,.webp,.mp4,.avi,.mov,.wmv,.flv,.mp3,.wav,.flac,.aac,.m4a"
            />
            <label htmlFor="file-upload">
              <Button 
                variant="outline" 
                disabled={disabled}
                className="cursor-pointer"
                asChild
              >
                <span>Choose Files</span>
              </Button>
            </label>
            <p className="text-xs text-gray-500 mt-2">
              Supports: PDF, DOCX, Images, Videos, Audio files
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="mt-6">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Selected Files</h4>
          <div className="space-y-3">
            {selectedFiles.map((file, index) => (
              <Card key={index} className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(file.name)}
                    <div>
                      <p className="font-medium text-gray-900">{file.name}</p>
                      <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(index)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
