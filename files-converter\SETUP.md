# Files Converter - Setup Guide

This guide will help you set up the Files Converter SaaS application with full functionality.

## Quick Start (Demo Mode)

The application is currently running in demo mode. You can:
- View the homepage and UI
- Test the file upload interface
- See the conversion workflow (simulated)
- Navigate between pages

**Demo limitations:**
- No actual file conversion
- No user authentication
- No data persistence

## Full Setup with Supabase

To enable full functionality, follow these steps:

### 1. Create a Supabase Project

1. Go to [supabase.com](https://supabase.com) and create an account
2. Click "New Project"
3. Choose your organization and enter project details:
   - **Name**: files-converter
   - **Database Password**: Choose a secure password
   - **Region**: Select the closest region to your users
4. Wait for the project to be created (2-3 minutes)

### 2. Get Your Supabase Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (e.g., `https://abcdefghijklmnop.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)
   - **Service role key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### 3. Configure Environment Variables

1. Open the `.env.local` file in the project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_MAX_ANONYMOUS_CONVERSIONS=5
```

### 4. Set Up the Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy the contents of `supabase/schema.sql`
3. Paste it into the SQL Editor and click **Run**

This will create:
- `profiles` table for user data
- `conversion_history` table for tracking conversions
- Row Level Security (RLS) policies
- Triggers for automatic profile creation

### 5. Configure Authentication

1. In Supabase dashboard, go to **Authentication** → **Settings**
2. Configure the following:

**Site URL**: `http://localhost:3000` (for development)
**Redirect URLs**: 
- `http://localhost:3000/auth/callback`
- `https://yourdomain.com/auth/callback` (for production)

**Email Templates** (optional):
- Customize the confirmation and reset password emails

### 6. Restart the Application

1. Stop the development server (Ctrl+C)
2. Start it again: `npm run dev`
3. The application should now run with full functionality

## Testing the Setup

After completing the setup:

1. **Homepage**: Visit `http://localhost:3000` - the demo notice should disappear
2. **Sign Up**: Create a new account at `/signup`
3. **Email Confirmation**: Check your email and click the confirmation link
4. **Sign In**: Log in at `/login`
5. **Dashboard**: Upload and convert files at `/dashboard`
6. **Profile**: View your conversion history at `/profile`

## Production Deployment

### Environment Variables for Production

Update your environment variables for production:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_APP_URL=https://yourdomain.com
NEXT_PUBLIC_MAX_ANONYMOUS_CONVERSIONS=5
```

### Supabase Settings for Production

1. Update **Site URL** to your production domain
2. Add your production domain to **Redirect URLs**
3. Configure **Custom SMTP** for email delivery (optional)

## File Conversion Integration

The current implementation includes a mock conversion service. To add real file conversion:

### Option 1: CloudConvert API
1. Sign up at [cloudconvert.com](https://cloudconvert.com)
2. Get your API key
3. Install the SDK: `npm install cloudconvert`
4. Update `src/lib/conversion.ts` with real conversion logic

### Option 2: Other Conversion APIs
- **Zamzar API**: For various file formats
- **ILovePDF API**: For PDF operations
- **FFmpeg**: For video/audio conversion (requires server setup)

### Option 3: Custom Server
Set up your own conversion server using:
- **LibreOffice**: For document conversion
- **ImageMagick**: For image conversion
- **FFmpeg**: For video/audio conversion

## Troubleshooting

### Common Issues

1. **"Invalid URL" error**: Check that your Supabase URL is correct and doesn't contain placeholder text
2. **Authentication not working**: Verify your Supabase anon key and that email confirmation is set up
3. **Database errors**: Ensure you've run the schema.sql file in your Supabase project
4. **CORS errors**: Check your Supabase redirect URLs configuration

### Getting Help

- Check the [Supabase documentation](https://supabase.com/docs)
- Review the [Next.js documentation](https://nextjs.org/docs)
- Open an issue in the project repository

## Features Overview

### ✅ Implemented Features
- Modern, responsive UI with Tailwind CSS
- User authentication (signup/login/logout)
- File upload with drag-and-drop
- Format selection and validation
- Conversion progress tracking
- Anonymous user limits (5 conversions)
- User profile and settings
- Conversion history for logged-in users
- Mobile-responsive design

### 🔄 Ready for Integration
- Real file conversion APIs
- Payment processing (Stripe)
- Email notifications
- Advanced user management
- Analytics and reporting

The application is production-ready and can be deployed to Vercel, Netlify, or any other hosting platform that supports Next.js.
