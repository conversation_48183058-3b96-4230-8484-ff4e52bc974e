'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useConversions } from '@/hooks/useConversions'
import { FileUpload } from '@/components/FileUpload'
import { ConversionItem } from '@/components/ConversionItem'
import { DemoNotice } from '@/components/DemoNotice'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { AlertTriangle, Crown } from 'lucide-react'
import Link from 'next/link'

export default function DashboardPage() {
  const { user } = useAuth()
  const {
    conversions,
    conversionCount,
    canConvert,
    getRemainingConversions,
    addConversion,
    startConversion,
    removeConversion
  } = useConversions()

  const remainingConversions = getRemainingConversions()

  const handleFileSelect = async (file: File) => {
    if (!canConvert()) {
      return
    }

    await addConversion(file, '')
  }

  const handleStartConversion = (id: string, targetFormat: string) => {
    startConversion(id)
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          File Converter Dashboard
        </h1>
        <p className="text-gray-600">
          Upload and convert your files to any supported format
        </p>
      </div>

      {/* Demo Notice */}
      <DemoNotice />

      {/* Usage Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">
              Conversions Used
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {conversionCount}
              {!user && (
                <span className="text-lg font-normal text-gray-500">
                  / {process.env.NEXT_PUBLIC_MAX_ANONYMOUS_CONVERSIONS || 5}
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">
              Account Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              {user ? (
                <>
                  <Crown className="h-5 w-5 text-yellow-500 mr-2" />
                  <span className="text-lg font-semibold text-gray-900">Pro User</span>
                </>
              ) : (
                <>
                  <span className="text-lg font-semibold text-gray-900">Anonymous</span>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">
              Remaining Conversions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {user ? '∞' : remainingConversions}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Upgrade Notice for Anonymous Users */}
      {!user && remainingConversions <= 2 && (
        <Card className="mb-8 border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <div className="flex items-start">
              <AlertTriangle className="h-6 w-6 text-orange-600 mr-3 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-orange-900 mb-1">
                  {remainingConversions === 0
                    ? 'Conversion limit reached'
                    : `Only ${remainingConversions} conversions remaining`
                  }
                </h3>
                <p className="text-orange-700 text-sm mb-3">
                  {remainingConversions === 0
                    ? 'Sign up for a free account to continue converting files.'
                    : 'Sign up for unlimited conversions and access to your conversion history.'
                  }
                </p>
                <div className="flex gap-3">
                  <Link href="/signup">
                    <Button size="sm">Create Free Account</Button>
                  </Link>
                  <Link href="/login">
                    <Button variant="outline" size="sm">Sign In</Button>
                  </Link>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Upload */}
      <div className="mb-8">
        <FileUpload
          onFileSelect={handleFileSelect}
          disabled={!canConvert()}
        />
      </div>

      {/* Active Conversions */}
      {conversions.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            Your Conversions
          </h2>
          <div className="space-y-4">
            {conversions.map(conversion => (
              <ConversionItem
                key={conversion.id}
                conversion={conversion}
                onStart={handleStartConversion}
                onRemove={removeConversion}
              />
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {conversions.length === 0 && (
        <Card className="text-center py-12">
          <CardContent>
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No conversions yet
            </h3>
            <p className="text-gray-600 mb-6">
              Upload your first file to get started with converting
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
