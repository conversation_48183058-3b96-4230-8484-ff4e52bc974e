'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { FileConversion } from '@/types'
import { convertFile, generateSessionId } from '@/lib/conversion'
import { createClient } from '@/lib/supabase/client'

export function useConversions() {
  const { user } = useAuth()
  const [conversions, setConversions] = useState<FileConversion[]>([])
  const [conversionCount, setConversionCount] = useState(0)
  const [sessionId, setSessionId] = useState<string>('')

  let supabase: any = null
  try {
    supabase = createClient()
  } catch (error) {
    console.warn('Supabase not configured for conversions')
  }

  useEffect(() => {
    // Initialize session ID for anonymous users
    if (!user && !sessionId) {
      const newSessionId = generateSessionId()
      setSessionId(newSessionId)
      localStorage.setItem('files_converter_session', newSessionId)
    }

    // Load existing session ID from localStorage
    if (!user && !sessionId) {
      const existingSessionId = localStorage.getItem('files_converter_session')
      if (existingSessionId) {
        setSessionId(existingSessionId)
      }
    }

    // Load conversion count
    loadConversionCount()
  }, [user, sessionId])

  const loadConversionCount = async () => {
    if (!supabase) {
      // Fallback to localStorage for demo purposes
      const localCount = localStorage.getItem('conversion_count')
      setConversionCount(localCount ? parseInt(localCount, 10) : 0)
      return
    }

    try {
      if (user) {
        // Load count for authenticated user
        const { data, error } = await supabase
          .from('conversion_history')
          .select('id')
          .eq('user_id', user.id)

        if (!error && data) {
          setConversionCount(data.length)
        }
      } else if (sessionId) {
        // Load count for anonymous user
        const { data, error } = await supabase
          .from('conversion_history')
          .select('id')
          .eq('session_id', sessionId)

        if (!error && data) {
          setConversionCount(data.length)
        }
      }
    } catch (error) {
      console.error('Error loading conversion count:', error)
      // Fallback to localStorage
      const localCount = localStorage.getItem('conversion_count')
      setConversionCount(localCount ? parseInt(localCount, 10) : 0)
    }
  }

  const canConvert = (): boolean => {
    if (user) return true // Authenticated users can convert unlimited files
    return conversionCount < parseInt(process.env.NEXT_PUBLIC_MAX_ANONYMOUS_CONVERSIONS || '5')
  }

  const getRemainingConversions = (): number => {
    if (user) return -1 // Unlimited for authenticated users
    return Math.max(0, parseInt(process.env.NEXT_PUBLIC_MAX_ANONYMOUS_CONVERSIONS || '5') - conversionCount)
  }

  const addConversion = async (file: File, targetFormat: string): Promise<string> => {
    const conversionId = Math.random().toString(36).substring(2)

    const newConversion: FileConversion = {
      id: conversionId,
      file,
      originalFormat: file.name.split('.').pop()?.toLowerCase() || '',
      targetFormat: targetFormat.toLowerCase(),
      status: 'pending',
      progress: 0
    }

    setConversions(prev => [...prev, newConversion])

    // Save to database if Supabase is configured
    if (supabase) {
      try {
        const { error } = await supabase
          .from('conversion_history')
          .insert({
            user_id: user?.id || null,
            session_id: user ? null : sessionId,
            original_filename: file.name,
            original_format: newConversion.originalFormat,
            target_format: targetFormat.toLowerCase(),
            file_size: file.size,
            status: 'pending'
          })

        if (error) {
          console.error('Error saving conversion to database:', error)
        }
      } catch (error) {
        console.error('Error saving conversion:', error)
      }
    }

    return conversionId
  }

  const updateConversion = (id: string, updates: Partial<FileConversion>) => {
    setConversions(prev =>
      prev.map(conv =>
        conv.id === id ? { ...conv, ...updates } : conv
      )
    )
  }

  const startConversion = async (id: string) => {
    const conversion = conversions.find(c => c.id === id)
    if (!conversion) return

    updateConversion(id, { status: 'processing' })

    try {
      const result = await convertFile(
        conversion.file,
        conversion.targetFormat,
        (progress) => updateConversion(id, { progress })
      )

      if (result.success) {
        updateConversion(id, {
          status: 'completed',
          downloadUrl: result.downloadUrl,
          progress: 100
        })
        setConversionCount(prev => {
          const newCount = prev + 1
          // Store in localStorage as fallback
          localStorage.setItem('conversion_count', newCount.toString())
          return newCount
        })
      } else {
        updateConversion(id, {
          status: 'failed',
          error: result.error || 'Conversion failed'
        })
      }
    } catch (error) {
      updateConversion(id, {
        status: 'failed',
        error: 'An unexpected error occurred'
      })
    }
  }

  const removeConversion = (id: string) => {
    setConversions(prev => prev.filter(conv => conv.id !== id))
  }

  return {
    conversions,
    conversionCount,
    canConvert,
    getRemainingConversions,
    addConversion,
    updateConversion,
    startConversion,
    removeConversion
  }
}
