import { SupportedFormat, ConversionOption } from '@/types'

export const SUPPORTED_CONVERSIONS: ConversionOption[] = [
  {
    category: 'document',
    from: ['pdf', 'docx', 'doc', 'txt', 'rtf'],
    to: ['pdf', 'docx', 'txt', 'rtf']
  },
  {
    category: 'image',
    from: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'],
    to: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp']
  },
  {
    category: 'video',
    from: ['mp4', 'avi', 'mov', 'wmv', 'flv'],
    to: ['mp4', 'avi', 'mov', 'wmv']
  },
  {
    category: 'audio',
    from: ['mp3', 'wav', 'flac', 'aac', 'm4a'],
    to: ['mp3', 'wav', 'flac', 'aac']
  }
]

export function getAvailableFormats(inputFormat: string): SupportedFormat[] {
  const conversion = SUPPORTED_CONVERSIONS.find(conv => 
    conv.from.includes(inputFormat.toLowerCase() as SupportedFormat)
  )
  return conversion ? conversion.to.filter(format => format !== inputFormat.toLowerCase()) : []
}

export function isFormatSupported(format: string): boolean {
  return SUPPORTED_CONVERSIONS.some(conv => 
    conv.from.includes(format.toLowerCase() as SupportedFormat)
  )
}

export function getFileCategory(format: string): string {
  const conversion = SUPPORTED_CONVERSIONS.find(conv => 
    conv.from.includes(format.toLowerCase() as SupportedFormat)
  )
  return conversion?.category || 'unknown'
}

// Mock conversion function - in a real app, this would call an external API
export async function convertFile(
  file: File, 
  targetFormat: string,
  onProgress?: (progress: number) => void
): Promise<{ success: boolean; downloadUrl?: string; error?: string }> {
  return new Promise((resolve) => {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.random() * 20
      if (onProgress) {
        onProgress(Math.min(progress, 90))
      }
      
      if (progress >= 90) {
        clearInterval(interval)
        if (onProgress) {
          onProgress(100)
        }
        
        // Simulate conversion completion
        setTimeout(() => {
          // In a real app, this would be the actual download URL
          const mockDownloadUrl = URL.createObjectURL(file)
          resolve({
            success: true,
            downloadUrl: mockDownloadUrl
          })
        }, 500)
      }
    }, 200)
  })
}

export function generateSessionId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}
