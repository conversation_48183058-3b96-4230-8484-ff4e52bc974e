'use client'

import { useState } from 'react'
import { FileConversion } from '@/types'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { 
  FileText, 
  Image as ImageIcon, 
  Video, 
  Music, 
  Download, 
  Trash2,
  ArrowR<PERSON>,
  Loader2
} from 'lucide-react'
import { getAvailableFormats, getFileCategory } from '@/lib/conversion'
import { formatFileSize } from '@/lib/utils'

interface ConversionItemProps {
  conversion: FileConversion
  onStart: (id: string, targetFormat: string) => void
  onRemove: (id: string) => void
}

export function ConversionItem({ conversion, onStart, onRemove }: ConversionItemProps) {
  const [selectedFormat, setSelectedFormat] = useState<string>('')
  const availableFormats = getAvailableFormats(conversion.originalFormat)

  const getFileIcon = (format: string) => {
    const category = getFileCategory(format)
    
    switch (category) {
      case 'document':
        return <FileText className="h-6 w-6 text-blue-600" />
      case 'image':
        return <ImageIcon className="h-6 w-6 text-green-600" />
      case 'video':
        return <Video className="h-6 w-6 text-purple-600" />
      case 'audio':
        return <Music className="h-6 w-6 text-orange-600" />
      default:
        return <FileText className="h-6 w-6 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600'
      case 'failed':
        return 'text-red-600'
      case 'processing':
        return 'text-blue-600'
      default:
        return 'text-gray-600'
    }
  }

  const handleStart = () => {
    if (selectedFormat) {
      onStart(conversion.id, selectedFormat)
    }
  }

  const handleDownload = () => {
    if (conversion.downloadUrl) {
      const link = document.createElement('a')
      link.href = conversion.downloadUrl
      link.download = `${conversion.file.name.split('.')[0]}.${conversion.targetFormat}`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <Card className="p-6">
      <CardContent className="p-0">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getFileIcon(conversion.originalFormat)}
            <div>
              <h3 className="font-medium text-gray-900">{conversion.file.name}</h3>
              <p className="text-sm text-gray-500">
                {formatFileSize(conversion.file.size)} • {conversion.originalFormat.toUpperCase()}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onRemove(conversion.id)}
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        {conversion.status === 'pending' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Convert to:
              </label>
              <select
                value={selectedFormat}
                onChange={(e) => setSelectedFormat(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select format...</option>
                {availableFormats.map(format => (
                  <option key={format} value={format}>
                    {format.toUpperCase()}
                  </option>
                ))}
              </select>
            </div>
            <Button
              onClick={handleStart}
              disabled={!selectedFormat}
              className="w-full"
            >
              <ArrowRight className="h-4 w-4 mr-2" />
              Start Conversion
            </Button>
          </div>
        )}

        {conversion.status === 'processing' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                Converting to {conversion.targetFormat.toUpperCase()}...
              </span>
              <span className="text-sm text-gray-500">{Math.round(conversion.progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${conversion.progress}%` }}
              />
            </div>
            <div className="flex items-center justify-center text-blue-600">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Processing...
            </div>
          </div>
        )}

        {conversion.status === 'completed' && (
          <div className="space-y-4">
            <div className="flex items-center text-green-600">
              <span className="text-sm font-medium">
                Successfully converted to {conversion.targetFormat.toUpperCase()}
              </span>
            </div>
            <Button
              onClick={handleDownload}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Download File
            </Button>
          </div>
        )}

        {conversion.status === 'failed' && (
          <div className="space-y-4">
            <div className="text-red-600">
              <p className="text-sm font-medium">Conversion failed</p>
              {conversion.error && (
                <p className="text-xs text-red-500 mt-1">{conversion.error}</p>
              )}
            </div>
            <Button
              onClick={() => onStart(conversion.id, conversion.targetFormat)}
              variant="outline"
              className="w-full"
            >
              Try Again
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
