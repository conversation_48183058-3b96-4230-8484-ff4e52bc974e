# Supabase clients for use in SSR frameworks

This package is useful for using the [Supabase JavaScript library](https://supabase.com/docs/reference/javascript/introduction) in
server-side rendering frameworks.

It provides a framework-agnostic way of creating a Supabase client.

Please refer to the [official server-side rendering guides](https://supabase.com/docs/guides/auth/server-side) for the latest best practices on using this package in your SSR framework of choice.
